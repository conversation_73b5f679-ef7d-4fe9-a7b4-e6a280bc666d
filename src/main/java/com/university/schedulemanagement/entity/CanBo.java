package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * CanBo Entity - Giảng viên/Cán bộ
 */
@Entity
@Table(name = "CAN_BO")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CanBo extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_CAN_BO")
    private Long idCanBo;

    @Column(name = "ID_VAI_TRO", nullable = false)
    private Long idVaiTro;

    @Column(name = "ID_PBMM", nullable = false)
    private Long idPbmm;

    @Column(name = "MA_CAN_BO", unique = true, nullable = false, length = 50)
    private String maCanBo;

    @JsonIgnore
    @Column(name = "MAT_KHAU", nullable = false, length = 250)
    private String matKhau;

    @Column(name = "TEN", nullable = false, length = 250)
    private String ten;

    @Column(name = "NGAY_SINH")
    private LocalDate ngaySinh;

    @Column(name = "NU")
    private Boolean nu = false;

    @JsonIgnore
    @Column(name = "MA_KICH_HOAT_OTP", length = 250)
    private String maKichHoatOtp;

    @JsonIgnore
    @Column(name = "THOI_HAN_OTP")
    private LocalDateTime thoiHanOtp;

    @Column(name = "SDT", length = 45)
    private String sdt;

    @Column(name = "EMAIL", length = 100)
    private String email;

    @Column(name = "TRANG_THAI")
    private Boolean trangThai = true;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_VAI_TRO", insertable = false, updatable = false)
    private VaiTro vaiTro;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_PBMM", insertable = false, updatable = false)
    private PBMM pbmm;

    @OneToMany(mappedBy = "canBo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Nhom> nhomList;

    @OneToMany(mappedBy = "canBo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;

    @OneToMany(mappedBy = "canBo", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<ThongKeGioGiang> thongKeGioGiangList;

    // Helper methods
    public String getFullInfo() {
        return String.format("%s - %s (%s)", maCanBo, ten, pbmm != null ? pbmm.getTenKhoa() : "");
    }

    public String getGenderText() {
        return nu ? "Nữ" : "Nam";
    }
}