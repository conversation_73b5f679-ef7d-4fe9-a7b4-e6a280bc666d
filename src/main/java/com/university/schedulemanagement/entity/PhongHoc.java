package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 * PhongHoc Entity - Phòng học
 */
@Entity
@Table(name = "PHONG_HOC")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class PhongHoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_PHONG")
    private Long idPhong;

    @Column(name = "MA_PHONG", nullable = false, length = 20)
    private String maPhong;

    @Column(name = "TEN_PHONG", nullable = false, length = 100)
    private String tenPhong;

    @Column(name = "ID_CO_SO", nullable = false)
    private Long idCoSo;

    @Column(name = "LOAI_PHONG", nullable = false, length = 10)
    private String loaiPhong; // LT, TH

    @Column(name = "SUC_CHUA")
    private Integer sucChua = 0;

    @Column(name = "TRANG_THAI")
    private Boolean trangThai = true;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_CO_SO", insertable = false, updatable = false)
    private CoSo coSo;

    @OneToMany(mappedBy = "phongHoc", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;

    public String getPhongInfo() {
        return String.format("%s - %s (%s)", maPhong, tenPhong,
                coSo != null ? coSo.getTenCoSo() : "");
    }

    public Boolean isPhongLyThuyet() {
        return "LT".equals(loaiPhong);
    }

    public Boolean isPhongThucHanh() {
        return "TH".equals(loaiPhong);
    }
}