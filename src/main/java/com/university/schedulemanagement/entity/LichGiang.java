package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * LichGiang Entity - Lịch giảng
 */
@Entity
@Table(name = "LICH_GIANG")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class LichGiang extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_LICH_GIANG")
    private Long idLichGiang;

    @Column(name = "ID_HOC_KY", nullable = false)
    private Long idHocKy;

    @Column(name = "ID_LOP", nullable = false)
    private Long idLop;

    @Column(name = "ID_MON_HOC", nullable = false)
    private Long idMonHoc;

    @Column(name = "ID_CAN_BO", nullable = false)
    private Long idCanBo;

    @Column(name = "ID_HINH_THUC", nullable = false)
    private Long idHinhThuc;

    @Column(name = "ID_PHONG", nullable = false)
    private Long idPhong;

    @Column(name = "ID_BUOI", nullable = false)
    private Long idBuoi;

    @Column(name = "THU_HOC", nullable = false)
    private Integer thuHoc; // 2-8 (Thứ 2 đến Chủ nhật)

    @Column(name = "SO_TIET", nullable = false)
    private Integer soTiet;

    @Column(name = "HE_SO", precision = 3, scale = 2)
    private BigDecimal heSo = BigDecimal.ONE;

    @Column(name = "NHOM_TH", length = 50)
    private String nhomTh; // Nhóm thực hành nếu có

    @Column(name = "TUAN_HOC", length = 100)
    private String tuanHoc; // Các tuần học (VD: 1-15, 1-8,10-15)

    @Column(name = "GHI_CHU", columnDefinition = "TEXT")
    private String ghiChu;

    @Column(name = "TRANG_THAI")
    private Boolean trangThai = true;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_HOC_KY", insertable = false, updatable = false)
    private HocKy hocKy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_LOP", insertable = false, updatable = false)
    private LopHoc lopHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_MON_HOC", insertable = false, updatable = false)
    private MonHoc monHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_CAN_BO", insertable = false, updatable = false)
    private CanBo canBo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_HINH_THUC", insertable = false, updatable = false)
    private HinhThucHoc hinhThucHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_PHONG", insertable = false, updatable = false)
    private PhongHoc phongHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_BUOI", insertable = false, updatable = false)
    private BuoiHoc buoiHoc;

    // Helper methods
    public String getThuHocText() {
        switch (thuHoc) {
            case 2: return "Thứ 2";
            case 3: return "Thứ 3";
            case 4: return "Thứ 4";
            case 5: return "Thứ 5";
            case 6: return "Thứ 6";
            case 7: return "Thứ 7";
            case 8: return "Chủ nhật";
            default: return "Không xác định";
        }
    }

    public BigDecimal getSoGioQuyDoi() {
        if (soTiet == null || heSo == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(soTiet).multiply(heSo);
    }

    public String getScheduleInfo() {
        return String.format("%s - %s - %s - %s",
                getThuHocText(),
                buoiHoc != null ? buoiHoc.getTenBuoi() : "",
                phongHoc != null ? phongHoc.getTenPhong() : "",
                hinhThucHoc != null ? hinhThucHoc.getTenHinhThuc() : ""
        );
    }

    public Boolean isThucHanh() {
        return hinhThucHoc != null && "TH".equals(hinhThucHoc.getTenHinhThuc());
    }

    public Boolean isLyThuyet() {
        return hinhThucHoc != null && "LT".equals(hinhThucHoc.getTenHinhThuc());
    }
}