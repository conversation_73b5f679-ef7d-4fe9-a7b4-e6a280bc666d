package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * VaiTro Entity - Vai trò
 */
@Entity
@Table(name = "VAI_TRO")
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class VaiTro {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_VAI_TRO")
    private Long idVaiTro;

    @Column(name = "TEN_VAI_TRO", nullable = false, length = 100)
    private String tenVaiTro;

    @Column(name = "MO_TA", length = 250)
    private String moTa;

    @OneToMany(mappedBy = "vaiTro", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<CanBo> canBoList;
}
