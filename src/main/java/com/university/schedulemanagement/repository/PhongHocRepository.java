package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * PhongHocRepository - Repository cho Phòng học
 */
@Repository
public interface PhongHocRepository extends JpaRepository<PhongHoc, Long> {

    List<PhongHoc> findByIdCoSoAndTrangThaiTrue(Long idCoSo);

    List<PhongHoc> findByLoaiPhongAndTrangThaiTrue(String loaiPhong);

    List<PhongHoc> findByIdCoSoAndLoaiPhongAndTrangThaiTrue(Long idCoSo, String loaiPhong);

    @Query("SELECT ph FROM PhongHoc ph WHERE " +
            "(LOWER(ph.tenPhong) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(ph.maPhong) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND ph.trangThai = true")
    Page<PhongHoc> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT ph FROM PhongHoc ph WHERE ph.idCoSo = :idCoSo AND ph.loaiPhong = :loaiPhong " +
            "AND ph.trangThai = true AND ph.idPhong NOT IN " +
            "(SELECT lg.idPhong FROM LichGiang lg WHERE lg.thuHoc = :thuHoc AND lg.idBuoi = :idBuoi " +
            "AND lg.idHocKy = :idHocKy AND lg.trangThai = true)")
    List<PhongHoc> findAvailableRooms(@Param("idCoSo") Long idCoSo,
                                      @Param("loaiPhong") String loaiPhong,
                                      @Param("thuHoc") Integer thuHoc,
                                      @Param("idBuoi") Long idBuoi,
                                      @Param("idHocKy") Long idHocKy);
}