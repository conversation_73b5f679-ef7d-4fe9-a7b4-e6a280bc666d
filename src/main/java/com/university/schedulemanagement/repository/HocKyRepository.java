package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * HocKyRepository - Repository cho <PERSON>ọ<PERSON> kỳ
 */
@Repository
public interface HocKyRepository extends JpaRepository<HocKy, Long> {

    Optional<HocKy> findByHienTaiTrue();

    List<HocKy> findByIdNienKhoa(Long idNienKhoa);

    @Query("SELECT hk FROM HocKy hk ORDER BY hk.ngayBatDau DESC")
    List<HocKy> findAllOrderByDateDesc();

    @Query("SELECT hk FROM HocKy hk WHERE hk.ngayBatDau <= :currentDate AND hk.ngayKetThuc >= :currentDate")
    List<HocKy> findActiveHocKy(@Param("currentDate") LocalDate currentDate);
}