package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * PBMMRepository - Repository cho Khoa/Phòng ban
 */
@Repository
public interface PBMMRepository extends JpaRepository<PBMM, Long> {

    Optional<PBMM> findByMaKhoa(String maKhoa);

    @Query("SELECT p FROM PBMM p WHERE " +
            "(LOWER(p.tenKhoa) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(p.maKhoa) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<PBMM> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
}